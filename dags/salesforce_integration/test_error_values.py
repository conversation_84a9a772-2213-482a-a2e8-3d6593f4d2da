#!/usr/bin/env python3
"""
Script de teste para verificar se os valores de erro estão sendo mostrados corretamente
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from salesforce_client import SalesforceClient

def test_error_value_extraction():
    """Testa a extração de valores que causaram erro"""
    
    # Cria cliente
    client = SalesforceClient()
    
    # Simula dados com problemas
    test_data = [
        {'Email': 'email_invalido', 'valor_parcelas': '123.45'},  # Item 1
        {'Email': '<EMAIL>', 'valor_parcelas': 'abc'},  # Item 2 - valor inválido
        {'Email': 'outro_email_ruim', 'valor_parcelas': '67.89'},  # Item 3
        {'Email': '<EMAIL>', 'valor_parcelas': 'xyz123'},  # Item 4 - valor inválido
    ]
    
    # Simula request_id
    request_id = "test-request-123"
    
    # Adiciona dados ao cache
    client.batch_data_cache[request_id] = {
        'data': test_data,
        'table_name': 'tb_propostas'
    }
    
    # Testa diferentes tipos de erro
    test_cases = [
        {
            'error_msg': 'The value for column [Email] is not a valid email address. Parse error [InvalidEmailAddress]',
            'items': [1, 3],
            'expected_field': 'Email'
        },
        {
            'error_msg': 'The value for column [valor_parcelas] is not a valid decimal. Parse error [InvalidDecimal]',
            'items': [2, 4],
            'expected_field': 'valor_parcelas'
        }
    ]
    
    print("=== TESTE DE EXTRAÇÃO DE VALORES DE ERRO ===\n")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Teste {i}: {test_case['error_msg']}")
        print(f"Itens afetados: {test_case['items']}")
        
        # Chama método de extração
        sample_values = client._get_sample_error_values(
            request_id, 
            test_case['items'], 
            test_case['error_msg']
        )
        
        print(f"Valores extraídos: {sample_values}")
        print(f"Campo detectado: {test_case['expected_field']}")
        print("-" * 50)
    
    # Limpa cache
    client.batch_data_cache.clear()
    print("✅ Teste concluído!")

if __name__ == "__main__":
    test_error_value_extraction()
